# 多层嵌套iframe划词工具栏和便签位置修复

## 问题描述

在多层嵌套的iframe环境中，当iframe有滚动条时，划词工具栏和便签的位置计算不正确，导致：

1. 划词工具栏出现在错误的位置
2. 创建的便签位置偏移
3. 滚动后位置更加错乱

## 根本原因

原有的位置计算逻辑没有考虑iframe层级中的滚动偏移，只计算了iframe的边界偏移，忽略了每一层iframe的滚动状态对最终位置的影响。

## 修复方案

### 1. 添加iframe滚动偏移计算函数

在多个文件中添加了 `_getIframeScrollAdjustment()` 或 `getIframeScrollAdjustment()` 函数：

- `entrypoints/content/annotator/adder.tsx`
- `entrypoints/content/annotator/guest.ts`  
- `utils/notes.ts`

这些函数的作用：
- 检测当前是否在iframe环境中
- 向上遍历所有iframe层级
- 累积每一层的滚动偏移
- 返回总的滚动偏移调整值

### 2. 修复划词工具栏位置计算

在 `adder.tsx` 的 `_showAt` 方法中：
- 获取iframe滚动偏移调整值
- 将调整值应用到工具栏的left和top位置计算中

### 3. 修复便签位置计算

在多个位置修复了便签的位置计算：

#### a) 创建便签时的坐标计算 (`guest.ts`)
- iframe划词：在 `iframeGlobalRect` 基础上加上滚动偏移
- 页面划词：在 `getBoundingClientRect()` 基础上加上滚动偏移

#### b) 便签样式计算 (`adder.tsx`)
- 在 `calculationStyle` 方法中应用滚动偏移调整

#### c) 便签重新渲染 (`utils/notes.ts`)
- 在 `reRenderNote` 函数中应用滚动偏移调整
- 在 `createNote` 函数中应用滚动偏移调整

### 4. 改进iframe消息传递

在 `iframe-listener.js` 中：
- 改进了滚动偏移信息的实时获取
- 确保发送消息时包含最新的滚动状态

## 修复的文件列表

1. `entrypoints/content/annotator/adder.tsx`
   - 添加 `_getIframeScrollAdjustment()` 方法
   - 修复 `_showAt()` 方法中的位置计算
   - 修复 `calculationStyle()` 方法中的便签位置计算

2. `entrypoints/content/annotator/guest.ts`
   - 添加 `_getIframeScrollAdjustment()` 方法
   - 修复 `createAnnotation()` 方法中的坐标计算

3. `utils/notes.ts`
   - 添加 `getIframeScrollAdjustment()` 函数
   - 修复 `reRenderNote()` 函数中的位置计算
   - 修复 `createNote()` 函数中的坐标计算

4. `entrypoints/iframe-listener.js`
   - 改进滚动偏移信息的实时获取

## 测试方法

使用提供的 `test-iframe-positioning.html` 文件进行测试：

1. 在浏览器中打开测试文件
2. 滚动页面到不同位置
3. 在各个iframe中选择文本
4. 检查划词工具栏是否出现在正确位置
5. 创建便签并检查位置是否正确
6. 在有滚动条的iframe中测试滚动后的位置

## 技术细节

### 滚动偏移计算逻辑

```javascript
private _getIframeScrollAdjustment(): { left: number; top: number } {
  if (window === window.top) {
    return { left: 0, top: 0 };
  }

  let totalScrollLeft = 0;
  let totalScrollTop = 0;
  let currentWindow: Window = window;

  while (currentWindow !== window.top && currentWindow.parent !== currentWindow) {
    try {
      const scrollLeft = currentWindow.pageXOffset || 
                        currentWindow.document.documentElement.scrollLeft || 
                        currentWindow.document.body.scrollLeft || 0;
      const scrollTop = currentWindow.pageYOffset || 
                       currentWindow.document.documentElement.scrollTop || 
                       currentWindow.document.body.scrollTop || 0;
      
      totalScrollLeft += scrollLeft;
      totalScrollTop += scrollTop;
      
      currentWindow = currentWindow.parent;
    } catch (e) {
      break; // 跨域限制
    }
  }

  return { left: totalScrollLeft, top: totalScrollTop };
}
```

### 位置计算应用

```javascript
// 工具栏位置
let rLeft = Math.min(left - parentRect.left + iframeScrollAdjustment.left, maxLeft);
let rTop = top - parentRect.top + iframeScrollAdjustment.top;

// 便签位置
const adjustedLeft = (coordinates.left || 0) + iframeScrollAdjustment.left;
const adjustedTop = (coordinates.top || 0) + iframeScrollAdjustment.top;
```

## 兼容性说明

- 修复方案向后兼容，不影响非iframe环境的使用
- 在跨域iframe环境中会优雅降级，使用现有的消息传递机制
- 支持任意层级的iframe嵌套

## 预期效果

修复后，在多层嵌套iframe环境中：
1. 划词工具栏会准确出现在选中文本附近
2. 便签会创建在正确的位置
3. 滚动后位置计算仍然准确
4. 支持复杂的嵌套场景和滚动场景
