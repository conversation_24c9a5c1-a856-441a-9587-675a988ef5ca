<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多层嵌套iframe划词位置测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 2000px; /* 增加页面高度以测试滚动 */
        }
        
        .container {
            margin-bottom: 30px;
            border: 2px solid #333;
            padding: 10px;
        }
        
        .scrollable {
            height: 300px;
            overflow: auto;
            border: 1px solid #ccc;
            padding: 10px;
        }
        
        .content {
            height: 600px;
            padding: 20px;
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
        }
        
        iframe {
            width: 100%;
            height: 400px;
            border: 2px solid #666;
            margin: 10px 0;
        }
        
        .test-text {
            background-color: #ffffcc;
            padding: 10px;
            margin: 10px 0;
            border: 1px dashed #999;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #666;
            padding-bottom: 5px;
        }
        
        .instructions {
            background-color: #e6f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>测试说明</h2>
        <p>这个页面用于测试多层嵌套iframe中划词工具栏和便签位置的修复效果：</p>
        <ol>
            <li>滚动页面到不同位置</li>
            <li>在各个iframe中选择文本</li>
            <li>检查划词工具栏是否出现在正确位置</li>
            <li>创建便签并检查位置是否正确</li>
            <li>在有滚动条的iframe中测试滚动后的位置</li>
        </ol>
    </div>

    <div class="container">
        <h2>顶层页面文本</h2>
        <div class="test-text">
            这是顶层页面的测试文本。请选择这段文字来测试划词功能。工具栏应该出现在选中文本附近的正确位置。
        </div>
    </div>

    <div class="container">
        <h2>第一层iframe</h2>
        <iframe srcdoc="
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        font-family: Arial, sans-serif;
                        height: 1000px;
                        background: linear-gradient(to bottom, #fff0f0, #ffe0e0);
                    }
                    .test-text { 
                        background-color: #ffcccc; 
                        padding: 10px; 
                        margin: 10px 0; 
                        border: 1px dashed #999; 
                    }
                    .scrollable {
                        height: 200px;
                        overflow: auto;
                        border: 1px solid #ccc;
                        padding: 10px;
                        margin: 20px 0;
                    }
                    .content {
                        height: 400px;
                        padding: 10px;
                        background: #f9f9f9;
                    }
                    iframe {
                        width: 100%;
                        height: 300px;
                        border: 2px solid #999;
                        margin: 10px 0;
                    }
                </style>
            </head>
            <body>
                <h3>第一层iframe内容</h3>
                <div class='test-text'>
                    这是第一层iframe中的测试文本。请选择这段文字来测试划词功能。
                </div>
                
                <div class='scrollable'>
                    <div class='content'>
                        <p>这是一个有滚动条的区域。请滚动后选择文本测试位置。</p>
                        <div class='test-text'>
                            滚动区域中的测试文本1。请选择这段文字。
                        </div>
                        <p>更多内容...</p>
                        <div class='test-text'>
                            滚动区域中的测试文本2。请选择这段文字。
                        </div>
                        <p>更多内容...</p>
                        <div class='test-text'>
                            滚动区域中的测试文本3。请选择这段文字。
                        </div>
                    </div>
                </div>

                <h3>嵌套的第二层iframe</h3>
                <iframe srcdoc=\"
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <style>
                            body { 
                                margin: 0; 
                                padding: 20px; 
                                font-family: Arial, sans-serif;
                                height: 800px;
                                background: linear-gradient(to bottom, #f0fff0, #e0ffe0);
                            }
                            .test-text { 
                                background-color: #ccffcc; 
                                padding: 10px; 
                                margin: 10px 0; 
                                border: 1px dashed #999; 
                            }
                            .scrollable {
                                height: 150px;
                                overflow: auto;
                                border: 1px solid #ccc;
                                padding: 10px;
                                margin: 20px 0;
                            }
                            .content {
                                height: 300px;
                                padding: 10px;
                                background: #f5f5f5;
                            }
                        </style>
                    </head>
                    <body>
                        <h4>第二层iframe内容</h4>
                        <div class='test-text'>
                            这是第二层iframe中的测试文本。请选择这段文字来测试嵌套iframe中的划词功能。
                        </div>
                        
                        <div class='scrollable'>
                            <div class='content'>
                                <p>第二层iframe中的滚动区域。</p>
                                <div class='test-text'>
                                    深层嵌套滚动区域中的测试文本1。
                                </div>
                                <p>更多内容...</p>
                                <div class='test-text'>
                                    深层嵌套滚动区域中的测试文本2。
                                </div>
                                <p>更多内容...</p>
                                <div class='test-text'>
                                    深层嵌套滚动区域中的测试文本3。
                                </div>
                            </div>
                        </div>
                        
                        <div class='test-text'>
                            第二层iframe底部的测试文本。请选择这段文字。
                        </div>
                    </body>
                    </html>
                \"></iframe>
                
                <div class='test-text'>
                    第一层iframe底部的测试文本。请选择这段文字。
                </div>
            </body>
            </html>
        "></iframe>
    </div>

    <div class="container">
        <h2>页面底部测试区域</h2>
        <div class="test-text">
            这是页面底部的测试文本。请滚动到这里后选择这段文字来测试划词功能。
        </div>
    </div>

    <script>
        // 添加一些调试信息
        console.log('测试页面加载完成');
        console.log('当前窗口是否为顶层:', window === window.top);
        console.log('页面滚动位置:', window.pageYOffset);
        
        // 监听滚动事件
        window.addEventListener('scroll', () => {
            console.log('页面滚动到:', window.pageYOffset);
        });
    </script>
</body>
</html>
